<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔥 Firebase Connection Test</h1>
        
        <div id="status-container">
            <div class="status info">🔄 Initializing Firebase test...</div>
        </div>

        <div>
            <button onclick="testFirebaseConnection()">🔄 Test Connection</button>
            <button onclick="testReadData()">📖 Test Read Data</button>
            <button onclick="testAnonymousAuth()">🔐 Test Anonymous Auth</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="results">
            <h3>📊 Test Results:</h3>
            <pre id="log-output">Waiting for tests...</pre>
        </div>
    </div>

    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        let logOutput = '';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logOutput += `[${timestamp}] ${message}\n`;
            document.getElementById('log-output').textContent = logOutput;
            
            // Also show status
            const statusContainer = document.getElementById('status-container');
            const statusClass = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            statusContainer.innerHTML = `<div class="status ${statusClass}">${message}</div>`;
            
            console.log(`[${timestamp}] ${message}`);
        }

        function clearResults() {
            logOutput = '';
            document.getElementById('log-output').textContent = 'Results cleared...';
            document.getElementById('status-container').innerHTML = '<div class="status info">Ready for testing...</div>';
        }

        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            log('✅ Firebase initialized successfully', 'success');
        } catch (error) {
            log(`❌ Firebase initialization failed: ${error.message}`, 'error');
        }

        function testFirebaseConnection() {
            log('🔄 Testing Firebase connection...');
            
            try {
                const database = firebase.database();
                
                // Test connection
                database.ref('.info/connected').once('value').then((snapshot) => {
                    if (snapshot.val() === true) {
                        log('✅ Firebase connection successful', 'success');
                    } else {
                        log('❌ Firebase not connected', 'error');
                    }
                }).catch(error => {
                    log(`❌ Connection test failed: ${error.message}`, 'error');
                });
                
            } catch (error) {
                log(`❌ Connection test error: ${error.message}`, 'error');
            }
        }

        function testReadData() {
            log('📖 Testing data read without authentication...');
            
            try {
                const database = firebase.database();
                
                // Test reading branches
                database.ref('branches').once('value').then((snapshot) => {
                    const data = snapshot.val();
                    if (data) {
                        log(`✅ Branches data read successfully: ${Object.keys(data).length} branches found`, 'success');
                        log(`📊 Branches data: ${JSON.stringify(data, null, 2)}`);
                    } else {
                        log('⚠️ No branches data found', 'warning');
                    }
                }).catch(error => {
                    log(`❌ Failed to read branches: ${error.message}`, 'error');
                    log(`❌ Error code: ${error.code}`);
                });
                
                // Test reading about section
                database.ref('aboutSection').once('value').then((snapshot) => {
                    const data = snapshot.val();
                    if (data) {
                        log(`✅ About section read successfully`, 'success');
                        log(`📊 About data: ${JSON.stringify(data, null, 2)}`);
                    } else {
                        log('⚠️ No about section data found', 'warning');
                    }
                }).catch(error => {
                    log(`❌ Failed to read about section: ${error.message}`, 'error');
                });
                
            } catch (error) {
                log(`❌ Read test error: ${error.message}`, 'error');
            }
        }

        function testAnonymousAuth() {
            log('🔐 Testing anonymous authentication...');
            
            try {
                const auth = firebase.auth();
                
                auth.signInAnonymously().then((userCredential) => {
                    const user = userCredential.user;
                    log(`✅ Anonymous authentication successful: ${user.uid}`, 'success');
                    
                    // Now try reading data with auth
                    setTimeout(() => {
                        log('📖 Testing data read with authentication...');
                        testReadData();
                    }, 1000);
                    
                }).catch(error => {
                    log(`❌ Anonymous authentication failed: ${error.message}`, 'error');
                    log(`❌ Error code: ${error.code}`);
                });
                
            } catch (error) {
                log(`❌ Auth test error: ${error.message}`, 'error');
            }
        }

        // Auto-run initial tests
        setTimeout(() => {
            testFirebaseConnection();
            setTimeout(testReadData, 2000);
        }, 1000);
    </script>
</body>
</html>
